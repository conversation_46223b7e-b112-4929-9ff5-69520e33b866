from flask import Flask, request, render_template, jsonify
import openai

app = Flask(__name__)

# Replace this with your actual OpenAI API key
openai.api_key = 'your-openai-api-key'

# Personality prompt for the girl chatbot
personality = """
You are <PERSON><PERSON>, a friendly, warm, and emotionally intelligent virtual girl.
You love talking about music, hobbies, daily life, and giving emotional support.
You are witty, caring, and use emojis sometimes to make your conversation feel natural.
Keep it casual, respectful, and kind.
"""

# Home page
@app.route('/')
def index():
    return render_template('index.html')

# Chat API
@app.route('/chat', methods=['POST'])
def chat():
    user_input = request.json['message']

    prompt = personality + f"\nUser: {user_input}\nAisha:"
    
    try:
        response = openai.Completion.create(
            engine="text-davinci-003",  # Or "gpt-3.5-turbo" with a different API call
            prompt=prompt,
            max_tokens=150,
            temperature=0.8,
            stop=["User:", "Aisha:"]
        )
        reply = response.choices[0].text.strip()
        return jsonify({'reply': reply})

    except Exception as e:
        return jsonify({'error': str(e)}), 500

if __name__ == '__main__':
    app.run(debug=True)
